# Docker相关文件
.env
.env.local
.env.*.local

# Docker数据卷和缓存
docker-data/
volumes/
data/

# 日志文件
*.log
logs/
log/

# 临时文件
*.tmp
*.temp
.tmp/
.temp/

# 操作系统生成的文件
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# IDE和编辑器文件
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# JetBrains IDEs
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# 备份文件
*.bak
*.backup
*.old
*.orig

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 证书和密钥文件
*.pem
*.key
*.crt
*.cert
*.p12
*.pfx

# 配置文件（包含敏感信息）
config.local.*
secrets.yml
secrets.yaml
.secrets

# Node.js (如果有前端开发)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python (如果有Python脚本)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 监控和性能分析
*.prof
*.out

# 测试覆盖率报告
coverage/
*.cover
.coverage
.coverage.*
htmlcov/

# 文档生成
docs/_build/
site/

# 其他
.cache/
.pytest_cache/
.mypy_cache/
.dmypy.json
dmypy.json

media/
static/
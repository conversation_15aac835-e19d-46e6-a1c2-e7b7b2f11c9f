# 防勒索病毒模拟演练平台 Docker 部署

## 服务说明

本项目包含以下服务：

- **PostgreSQL**: 主数据库 (端口: 5432)
- **Redis**: 缓存服务 (端口: 6379)
- **Backend**: Django后端服务 (端口: 8001)
- **Trojan Server**: 仿真病毒后端 (端口: 7891)
- **Frontend**: Nuxt.js前端服务 (端口: 3000)

## 快速启动

### 1. 启动所有服务
```bash
docker-compose up -d
```

### 2. 查看服务状态
```bash
docker-compose ps
```

### 3. 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f trojan-server
```

### 4. 停止服务
```bash
docker-compose down
```

### 5. 停止服务并删除数据卷
```bash
docker-compose down -v
```

## 服务访问地址

- 前端: http://localhost:3000
- 后端API: http://localhost:8000
- 仿真病毒后端: http://localhost:7891
- PostgreSQL: localhost:5432
- Redis: localhost:6379

## 数据持久化

- PostgreSQL数据存储在 `postgres_data` 数据卷中
- Redis数据存储在 `redis_data` 数据卷中

## 初始化脚本

如果需要在PostgreSQL启动时执行初始化SQL脚本，请将.sql文件放在 `init-scripts/` 目录中。

## 环境变量

主要环境变量已在docker-compose.yml中配置，如需修改可参考 `.env.example` 文件。

## 故障排除

### 1. 检查容器状态
```bash
docker-compose ps
```

### 2. 检查容器健康状态
```bash
docker-compose exec postgres pg_isready -U fls -d fls
docker-compose exec redis redis-cli ping
```

### 3. 重启特定服务
```bash
docker-compose restart backend
docker-compose restart frontend
```

### 4. 重新构建并启动
```bash
docker-compose up -d --force-recreate
```
